@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', 'إعدادات متطلبات التوثيق')

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="nav-align-top mb-4">
                <ul class="nav nav-pills mb-3" role="tablist">
                    <li class="nav-item">
                        <button type="button" class="nav-link active" role="tab" data-bs-toggle="tab" data-bs-target="#navs-pills-top-requirements" aria-controls="navs-pills-top-requirements" aria-selected="true">
                            <i class="ti ti-settings me-1"></i>
                            متطلبات التوثيق
                        </button>
                    </li>
                    <li class="nav-item">
                        <button type="button" class="nav-link" role="tab" data-bs-toggle="tab" data-bs-target="#navs-pills-top-pending" aria-controls="navs-pills-top-pending" aria-selected="false">
                            <i class="ti ti-clock me-1"></i>
                            الطلبات المعلقة
                        </button>
                    </li>
                </ul>
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="navs-pills-top-requirements" role="tabpanel">
                        @livewire('verification-requirements-form')
                    </div>
                    <div class="tab-pane fade" id="navs-pills-top-pending" role="tabpanel">
                        @livewire('verification-requests-table')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('page-script')
<script>
    // Verification management functions
    function verifyUser(userId) {
        Swal.fire({
            title: 'توثيق المستخدم',
            text: 'هل أنت متأكد من توثيق هذا المستخدم؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، وثق',
            cancelButtonText: 'إلغاء',
            input: 'textarea',
            inputPlaceholder: 'ملاحظات إضافية (اختياري)',
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/admin/verification/verify/${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        admin_notes: result.value
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم!', data.message, 'success');
                        location.reload();
                    } else {
                        Swal.fire('خطأ!', 'حدث خطأ أثناء التوثيق', 'error');
                    }
                });
            }
        });
    }

    function unverifyUser(userId) {
        Swal.fire({
            title: 'إلغاء توثيق المستخدم',
            text: 'هل أنت متأكد من إلغاء توثيق هذا المستخدم؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'نعم، ألغ التوثيق',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                fetch(`/admin/verification/unverify/${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire('تم!', 'تم إلغاء توثيق المستخدم', 'success');
                        location.reload();
                    } else {
                        Swal.fire('خطأ!', 'حدث خطأ أثناء إلغاء التوثيق', 'error');
                    }
                });
            }
        });
    }

    function viewVerification(userId) {
        fetch(`/admin/verification/details/${userId}`)
            .then(response => response.json())
            .then(data => {
                // Show verification details in modal
                // This would need a modal implementation
                console.log(data);
            });
    }
</script>
@endsection
