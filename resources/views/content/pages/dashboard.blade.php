@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', 'Home')

@section('page-style')
    @vite(['resources/assets/vendor/libs/apex-charts/apex-charts.scss'])
@endsection

@section('page-script')
    @include('content.components.dashboard.scripts')
@endsection

@section('content')
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-end">
                <a href="{{ route('dashboard.refresh') }}" class="btn btn-primary">
                    <i class="ti ti-refresh me-1"></i>
                    تحديث البيانات
                </a>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Dashboard Cards -->
        @include('content.components.dashboard.cards', ['dashboardCards' => $data['dashboardCards']])

        <!-- Statistics -->
        @include('content.components.dashboard.statistics', ['statistics' => $data['statistics']])

        <!-- Product Reports -->
        @include('content.components.dashboard.product-analytics', ['productReports' => $data['productReports']])

        <!-- User Reports -->
        @include('content.components.dashboard.user-reports', ['userReports' => $data['userReports']])

        <!-- Report Statistics -->
        @include('content.components.dashboard.report-stats', ['reportStats' => $data['reportStats']])

        <!-- Chat Analytics -->
        @include('content.components.dashboard.chat-analytics', ['chatAnalytics' => $data['chatAnalytics']])

        <!-- Ad Performance -->
        @include('content.components.dashboard.ad-performance', ['adPerformance' => $data['adPerformance']])

        <!-- Verification Statistics -->
        @include('content.components.dashboard.verification-stats', ['verificationStats' => $data['verificationStats']])

        <!-- Quick Access Links -->
        @include('content.components.dashboard.quick-access')

        <!-- Visitors Chart -->
        @include('content.components.dashboard.visitors-chart', [
            'weeklyVisits' => $data['weeklyVisits'],
            'weekLabels' => $data['weekLabels']
        ])

        <!-- Country Statistics -->
        @include('content.components.dashboard.country-stats', [
            'countryStats' => $data['countryStats']
        ])
        
        <!-- Device and Platform Statistics -->
        @include('content.components.dashboard.device-platform-stats', [
            'deviceStats' => $data['deviceStats'],
            'platformStats' => $data['platformStats']
        ])

        <!-- Most Visited Pages -->
        @include('content.components.dashboard.most-visited-pages', ['mostVisitedPages' => $data['mostVisitedPagesThisMonth']])
    </div>
@endsection
