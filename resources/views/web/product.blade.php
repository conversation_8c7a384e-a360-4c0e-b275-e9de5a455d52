@extends('web.layouts.layout')
@section('title', $product->title)

@section('page-meta')
<meta name="description" content="{{ \Illuminate\Support\Str::limit(strip_tags($product->desc), 160) }}">
<meta name="keywords" content="{{ $product->title }}, {{ $product->category->title }}, {{ __('حراجي, إعلانات, بيع, شراء') }}">
@endsection

@section('page-style')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
<style>
    .swiper-button-next,
    .swiper-button-prev {
        color: #fff;
        background: rgba(0, 0, 0, 0.3);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .swiper-button-next:after,
    .swiper-button-prev:after {
        font-size: 18px;
    }

    .swiper-pagination-bullet-active {
        background: var(--p);
    }

    .thumbnail-swiper .swiper-slide {
        opacity: 0.5;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .thumbnail-swiper .swiper-slide-thumb-active {
        opacity: 1;
        border-color: var(--p);
    }
</style>
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.categories') }}">{{ __('الأقسام') }}</a></li>
                @if($product->category->parent)
                    <li><a href="{{ route('web.category', $product->category->parent->slug) }}">{{ $product->category->parent->title }}</a></li>
                @endif
                <li><a href="{{ route('web.category', $product->category->slug) }}">{{ $product->category->title }}</a></li>
                <li>{{ $product->title }}</li>
            </ul>
        </div>

        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Main Content -->
            <div class="w-full lg:w-2/3">
                <!-- Product Images -->
                <div class="bg-base-200 rounded-lg overflow-hidden mb-8">
                    @if($product->media && $product->media->count() > 0)
                        <!-- Main Swiper -->
                        <div class="swiper main-swiper">
                            <div class="swiper-wrapper">
                                @foreach($product->media as $media)
                                    <div class="swiper-slide">
                                        <div class="aspect-[4/3] bg-base-300">
                                            <img src="{{ asset('storage/products/' . $media->watermarked_path) }}" alt="{{ $product->title }}" class="w-full h-full object-contain">
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-pagination"></div>
                        </div>

                        <!-- Thumbnail Swiper -->
                        @if($product->media->count() > 1)
                            <div class="swiper thumbnail-swiper mt-2">
                                <div class="swiper-wrapper">
                                    @foreach($product->media as $media)
                                        <div class="swiper-slide">
                                            <div class="aspect-square bg-base-300 cursor-pointer">
                                                <img src="{{ asset('storage/products/' . $media->path) }}" alt="{{ $product->title }}" class="w-full h-full object-cover">
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @else
                        <div class="aspect-[4/3] bg-base-300 flex items-center justify-center">
                            <i class="fas fa-image text-6xl text-base-content/30"></i>
                        </div>
                    @endif
                </div>

                <!-- Product Details -->
                <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
                    <h1 class="text-2xl font-bold mb-4">{{ $product->title }}</h1>

                    <div class="flex flex-wrap gap-4 mb-6">
                        <div class="badge badge-lg">{{ $product->condition == 'new' ? __('جديد') : __('مستعمل') }}</div>
                        <div class="badge badge-lg badge-outline">
                            <i class="fas fa-eye mr-1 rtl:ml-1 rtl:mr-0"></i> {{ $product->views_count }} {{ __('مشاهدة') }}
                        </div>
                        <div class="badge badge-lg badge-outline">
                            <i class="fas fa-calendar-alt mr-1 rtl:ml-1 rtl:mr-0"></i> {{ $product->created_at->format('Y-m-d') }}
                        </div>
                    </div>

                    <div class="text-3xl font-bold text-primary mb-6">
                        {{ number_format($product->price, 2) }} {{ __('ريال') }}
                    </div>

                    <div class="divider"></div>

                    <h2 class="text-xl font-bold mb-4">{{ __('الوصف') }}</h2>
                    <div class="prose max-w-none mb-6">
                        {!! nl2br(e($product->desc)) !!}
                    </div>

                    <div class="divider"></div>

                    <h2 class="text-xl font-bold mb-4">{{ __('التفاصيل') }}</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="flex items-center">
                            <span class="font-bold mr-2 rtl:ml-2 rtl:mr-0">{{ __('القسم') }}:</span>
                            <a href="{{ route('web.category', $product->category->slug) }}" class="text-primary hover:underline">
                                {{ $product->category->title }}
                            </a>
                        </div>

                        <div class="flex items-center">
                            <span class="font-bold mr-2 rtl:ml-2 rtl:mr-0">{{ __('الموقع') }}:</span>
                            <span>{{ $product->location->name ?? __('غير محدد') }}</span>
                        </div>

                        <div class="flex items-center">
                            <span class="font-bold mr-2 rtl:ml-2 rtl:mr-0">{{ __('الحالة') }}:</span>
                            <span>{{ $product->condition == 'new' ? __('جديد') : __('مستعمل') }}</span>
                        </div>

                        <div class="flex items-center">
                            <span class="font-bold mr-2 rtl:ml-2 rtl:mr-0">{{ __('تاريخ النشر') }}:</span>
                            <span>{{ $product->created_at->format('Y-m-d') }}</span>
                        </div>
                    </div>

                    @if($product->tags && $product->tags->count() > 0)
                        <div class="flex flex-wrap gap-2 mb-6">
                            @foreach($product->tags as $tag)
                                <a href="{{ route('web.search') }}?q={{ $tag->name }}" class="badge badge-outline hover:bg-primary hover:text-white transition-colors">
                                    {{ $tag->name }}
                                </a>
                            @endforeach
                        </div>
                    @endif

                    <div class="flex flex-wrap gap-4">
                        <button onclick="handleFavoriteClick('{{ $product->id }}', this)" class="btn btn-outline">
                            <i class="far fa-heart mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('إضافة للمفضلة') }}
                        </button>

                        @auth
                            <form action="{{ route('web.toggle.follow') }}" method="POST" class="inline">
                                @csrf
                                <input type="hidden" name="type" value="product">
                                <input type="hidden" name="id" value="{{ $product->id }}">
                                <button type="submit" class="btn btn-outline {{ auth()->user()->follows()->where('followable_type', 'App\\Models\\Product')->where('followable_id', $product->id)->exists() ? 'btn-primary' : '' }}">
                                    <i class="fas {{ auth()->user()->follows()->where('followable_type', 'App\\Models\\Product')->where('followable_id', $product->id)->exists() ? 'fa-bell-slash' : 'fa-bell' }} mr-2 rtl:ml-2 rtl:mr-0"></i>
                                    {{ auth()->user()->follows()->where('followable_type', 'App\\Models\\Product')->where('followable_id', $product->id)->exists() ? __('إلغاء المتابعة') : __('متابعة المنتج') }}
                                </button>
                            </form>
                        @endauth

                        <a href="{{ route('web.report', $product->id) }}" class="btn btn-outline btn-error">
                            <i class="fas fa-flag mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('الإبلاغ عن المنتج') }}
                        </a>

                        <div class="dropdown dropdown-end">
                            <button class="btn btn-outline btn-primary" tabindex="0">
                                <i class="fas fa-share-alt mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('مشاركة') }}
                            </button>
                            <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                <li>
                                    <a href="https://wa.me/?text={{ urlencode($product->title . ' - ' . route('web.product', $product->slug)) }}" target="_blank" class="flex items-center">
                                        <i class="fab fa-whatsapp text-green-500 text-lg"></i>
                                        <span>{{ __('واتساب') }}</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(route('web.product', $product->slug)) }}" target="_blank" class="flex items-center">
                                        <i class="fab fa-facebook text-blue-600 text-lg"></i>
                                        <span>{{ __('فيسبوك') }}</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="https://twitter.com/intent/tweet?text={{ urlencode($product->title) }}&url={{ urlencode(route('web.product', $product->slug)) }}" target="_blank" class="flex items-center">
                                        <i class="fab fa-twitter text-blue-400 text-lg"></i>
                                        <span>{{ __('تويتر') }}</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="https://t.me/share/url?url={{ urlencode(route('web.product', $product->slug)) }}&text={{ urlencode($product->title) }}" target="_blank" class="flex items-center">
                                        <i class="fab fa-telegram text-blue-500 text-lg"></i>
                                        <span>{{ __('تلجرام') }}</span>
                                    </a>
                                </li>
                                <li>
                                    <button onclick="copyProductLink()" class="flex items-center">
                                        <i class="fas fa-link text-gray-600 text-lg"></i>
                                        <span>{{ __('نسخ الرابط') }}</span>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
                    <h2 class="text-xl font-bold mb-6">{{ __('التعليقات') }} ({{ $product->comments->count() }})</h2>

                    @if(auth()->check())
                        <form action="{{ route('web.product.comment', $product) }}" method="POST" class="mb-8">
                            @csrf
                            <div class="mb-4">
                                <textarea name="content" rows="3" class="textarea textarea-bordered w-full" placeholder="{{ __('أضف تعليقك...') }}"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">{{ __('إرسال التعليق') }}</button>
                        </form>
                    @else
                        <div class="alert mb-8">
                            <div>
                                <i class="fas fa-info-circle"></i>
                                <span>{{ __('يجب عليك') }} <a href="{{ route('login') }}" class="text-primary font-bold hover:underline">{{ __('تسجيل الدخول') }}</a> {{ __('لإضافة تعليق') }}</span>
                            </div>
                        </div>
                    @endif

                    @if($product->comments && $product->comments->count() > 0)
                        <div class="space-y-6">
                            @foreach($product->comments as $comment)
                                <div class="flex gap-4">
                                    <div class="avatar">
                                        <div class="w-12 h-12 rounded-full">
                                                <img src="{{$comment->user->avatar()}}" alt="{{ $comment->user->name }}">
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex justify-between items-center mb-2">
                                            <h3 class="font-bold">{{ $comment->user->name }}</h3>
                                            <span class="text-sm text-base-content/70">{{ $comment->created_at->diffForHumans() }}</span>
                                        </div>
                                        <p>{{ $comment->text }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <i class="far fa-comment-dots text-4xl text-base-content/30 mb-2"></i>
                            <p>{{ __('لا توجد تعليقات حتى الآن') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="w-full lg:w-1/3">
                <!-- Seller Info -->
                <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
                    <h2 class="text-xl font-bold mb-4">{{ __('معلومات البائع') }}</h2>

                    <div class="flex items-center gap-4 mb-4">
                        <div class="avatar">
                            <div class="w-16 h-16 rounded-full">
                                    <img src="{{ $product->user->avatar() }}" alt="{{ $product->user->name }}">
                            </div>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">{{ $product->user->name }}</h3>
                            <p class="text-sm text-base-content/70">{{ __('عضو منذ') }} {{ $product->user->created_at->format('Y-m-d') }}</p>
                        </div>
                    </div>

                    <div class="space-y-3 mb-6">
                        @if($product->user->phone && $product->allow_phone_call)
                            <div class="flex items-center">
                                <i class="fas fa-phone-alt mr-2 rtl:ml-2 rtl:mr-0 text-primary"></i>
                                <span>{{ $product->alternative_phone ?: $product->user->phone }}</span>
                            </div>
                        @endif

                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 rtl:ml-2 rtl:mr-0 text-primary"></i>
                            <span>{{ $product->location->name ?? __('غير محدد') }}</span>
                        </div>
                    </div>

                    <div class="flex flex-col gap-2">
                        @if(auth()->check() && $product->allow_chat && $product->user->id != auth()->id())
                            <a href="{{ route('web.chat.start', ['user' => $product->user->id, 'product' => $product->id]) }}" class="btn btn-primary">
                                <i class="fas fa-comments mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('محادثة البائع') }}
                            </a>
                        @endif

                        <a href="{{ route('web.sellerProfile', $product->user->username) }}" class="btn btn-outline btn-primary">
                            <i class="fas fa-user mr-2 rtl:ml-2 rtl:mr-0"></i> {{ __('عرض الملف الشخصي') }}
                        </a>
                    </div>
                </div>

                <!-- Seller's Other Products -->
                @if($sellerProducts && $sellerProducts->count() > 0)
                    <div class="bg-base-100 rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-xl font-bold mb-4">{{ __('إعلانات أخرى للبائع') }}</h2>

                        <div class="space-y-4">
                            @foreach($sellerProducts as $sellerProduct)
                                <a href="{{ route('web.product', $sellerProduct->slug) }}" class="flex gap-3 hover:bg-base-200 p-2 rounded-lg transition-colors">
                                    <div class="w-20 h-20 bg-base-200 rounded-md overflow-hidden">
                                        @if($sellerProduct->primaryMediaUrl())
                                            <img src="{{ $sellerProduct->primaryMediaUrl() }}" alt="{{ $sellerProduct->title }}" class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full flex items-center justify-center">
                                                <i class="fas fa-image text-xl text-base-content/30"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-bold line-clamp-1">{{ $sellerProduct->title }}</h3>
                                        <p class="text-primary font-bold">{{ number_format($sellerProduct->price, 2) }} {{ __('ريال') }}</p>
                                        <p class="text-sm text-base-content/70">{{ $sellerProduct->created_at->diffForHumans() }}</p>
                                    </div>
                                </a>
                            @endforeach
                        </div>

                        <div class="mt-4">
                            <a href="{{ route('web.sellerProfile', $product->user->username) }}" class="btn btn-outline btn-sm w-full">
                                {{ __('عرض جميع الإعلانات') }}
                            </a>
                        </div>
                    </div>
                @endif

                <!-- Related Products -->
                @if($relatedProducts && $relatedProducts->count() > 0)
                    <div class="bg-base-100 rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-bold mb-4">{{ __('إعلانات مشابهة') }}</h2>

                        <div class="space-y-4">
                            @foreach($relatedProducts as $relatedProduct)
                                <a href="{{ route('web.product', $relatedProduct->slug) }}" class="flex gap-3 hover:bg-base-200 p-2 rounded-lg transition-colors">
                                    <div class="w-20 h-20 bg-base-200 rounded-md overflow-hidden">
                                        @if($relatedProduct->primaryMediaUrl())
                                            <img src="{{ $relatedProduct->primaryMediaUrl() }}" alt="{{ $relatedProduct->title }}" class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full flex items-center justify-center">
                                                <i class="fas fa-image text-xl text-base-content/30"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-bold line-clamp-1">{{ $relatedProduct->title }}</h3>
                                        <p class="text-primary font-bold">{{ number_format($relatedProduct->price, 2) }} {{ __('ريال') }}</p>
                                        <p class="text-sm text-base-content/70">{{ $relatedProduct->created_at->diffForHumans() }}</p>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</main>
@endsection

@section('page-script')
<script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize thumbnail swiper
        var thumbnailSwiper = new Swiper('.thumbnail-swiper', {
            spaceBetween: 10,
            slidesPerView: 4,
            freeMode: true,
            watchSlidesProgress: true,
        });

        // Initialize main swiper
        var mainSwiper = new Swiper('.main-swiper', {
            spaceBetween: 10,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            thumbs: {
                swiper: thumbnailSwiper,
            },
        });
    });

    // Function to copy product link to clipboard
    function copyProductLink() {
        const productUrl = '{{ route('web.product', $product->slug) }}';

        // Use the modern Clipboard API if available
        if (navigator.clipboard) {
            navigator.clipboard.writeText(productUrl)
                .then(showSuccessToast)
                .catch(err => {
                    console.error('Failed to copy: ', err);
                    fallbackCopyMethod();
                });
        } else {
            // Fallback for browsers that don't support Clipboard API
            fallbackCopyMethod();
        }
    }

    // Fallback copy method using document.execCommand
    function fallbackCopyMethod() {
        const productUrl = '{{ route('web.product', $product->slug) }}';

        // Create a temporary input element
        const tempInput = document.createElement('input');
        tempInput.value = productUrl;
        document.body.appendChild(tempInput);

        // Select and copy the text
        tempInput.select();
        document.execCommand('copy');

        // Remove the temporary element
        document.body.removeChild(tempInput);

        showSuccessToast();
    }

    // Show success toast notification
    function showSuccessToast() {
        // Show a toast notification
        const toast = document.createElement('div');
        toast.className = 'toast toast-end';
        toast.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <span>{{ __('تم نسخ الرابط بنجاح') }}</span>
            </div>
        `;
        document.body.appendChild(toast);

        // Remove the toast after 3 seconds
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    }

    // Make the function globally available
    window.copyProductLink = copyProductLink;
</script>
@endsection
