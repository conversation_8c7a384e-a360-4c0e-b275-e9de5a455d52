<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserVerification;
use App\Models\Settings;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Auth;

class VerificationController extends Controller
{
    /**
     * Display verification requirements form.
     */
    public function showRequirementsForm()
    {
        $requirements = Settings::where('key', 'verification_requirements')->first();
        $requirementsData = $requirements ? json_decode($requirements->value, true) : [];
        
        return view('modules.settings.verification-requirements', compact('requirementsData'));
    }

    /**
     * Store verification requirements.
     */
    public function storeRequirements(Request $request)
    {
        $request->validate([
            'requirements' => 'required|array',
            'requirements.*.type' => 'required|string',
            'requirements.*.title' => 'required|string',
            'requirements.*.description' => 'nullable|string',
            'requirements.*.is_required' => 'boolean',
        ]);

        Settings::updateOrCreate(
            ['key' => 'verification_requirements'],
            [
                'value' => json_encode($request->requirements),
                'type' => 'json',
                'group' => 'verification',
                'description' => 'User verification requirements configuration'
            ]
        );

        return redirect()->back()->with('success', 'تم حفظ متطلبات التوثيق بنجاح');
    }

    /**
     * Display user verification form.
     */
    public function showVerificationForm()
    {
        $user = Auth::user();
        
        // Check if user already has pending verification
        if ($user->hasPendingVerification()) {
            return redirect()->back()->with('info', 'لديك طلب توثيق قيد المراجعة');
        }

        // Check if user is already verified
        if ($user->isVerified()) {
            return redirect()->back()->with('info', 'حسابك موثق بالفعل');
        }

        $requirements = Settings::where('key', 'verification_requirements')->first();
        $requirementsData = $requirements ? json_decode($requirements->value, true) : [];
        
        return view('web.verification.form', compact('requirementsData'));
    }

    /**
     * Submit verification request.
     */
    public function submitVerification(Request $request)
    {
        $user = Auth::user();
        
        // Check if user already has pending verification
        if ($user->hasPendingVerification()) {
            return redirect()->back()->with('error', 'لديك طلب توثيق قيد المراجعة');
        }

        // Check if user is already verified
        if ($user->isVerified()) {
            return redirect()->back()->with('error', 'حسابك موثق بالفعل');
        }

        $requirements = Settings::where('key', 'verification_requirements')->first();
        $requirementsData = $requirements ? json_decode($requirements->value, true) : [];

        // Validate required fields
        $rules = ['user_type' => 'required|string'];
        foreach ($requirementsData as $requirement) {
            if ($requirement['is_required']) {
                if ($requirement['type'] === 'file') {
                    $rules[$requirement['title']] = 'required|file|max:10240'; // 10MB max
                } else {
                    $rules[$requirement['title']] = 'required|string';
                }
            }
        }

        $request->validate($rules);

        // Handle file uploads
        $uploadedFiles = [];
        foreach ($requirementsData as $requirement) {
            if ($requirement['type'] === 'file' && $request->hasFile($requirement['title'])) {
                $file = $request->file($requirement['title']);
                $filename = time() . '_' . $file->getClientOriginalName();
                $path = $file->storeAs('verification_documents', $filename, 'public');
                $uploadedFiles[$requirement['title']] = $path;
            }
        }

        // Prepare submitted data
        $submittedData = $request->except(['_token', 'user_type']);
        foreach (array_keys($uploadedFiles) as $key) {
            unset($submittedData[$key]); // Remove file data from submitted_data
        }

        // Create verification request
        UserVerification::create([
            'user_id' => $user->id,
            'user_type' => $request->user_type,
            'submitted_data' => $submittedData,
            'uploaded_files' => $uploadedFiles,
            'status' => 'pending',
        ]);

        return redirect()->back()->with('success', 'تم إرسال طلب التوثيق بنجاح. سيتم مراجعته قريباً');
    }

    /**
     * Approve verification.
     */
    public function approve(UserVerification $verification, Request $request)
    {
        $request->validate([
            'admin_notes' => 'nullable|string',
        ]);

        $verification->update([
            'status' => 'approved',
            'admin_notes' => $request->admin_notes,
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        // Mark user as verified
        $verification->user->markAsVerified(Auth::id(), $request->admin_notes);

        return response()->json(['success' => true, 'message' => 'تم قبول طلب التوثيق']);
    }

    /**
     * Reject verification.
     */
    public function reject(UserVerification $verification, Request $request)
    {
        $request->validate([
            'admin_notes' => 'required|string',
        ]);

        $verification->update([
            'status' => 'rejected',
            'admin_notes' => $request->admin_notes,
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        return response()->json(['success' => true, 'message' => 'تم رفض طلب التوثيق']);
    }

    /**
     * Get verification details.
     */
    public function getVerificationDetails(UserVerification $verification)
    {
        $verification->load(['user', 'reviewer']);

        return response()->json([
            'verification' => $verification,
            'requirements' => Settings::where('key', 'verification_requirements')->first()
        ]);
    }

    /**
     * Verify user directly (admin action).
     */
    public function verifyUser(User $user, Request $request)
    {
        $request->validate([
            'admin_notes' => 'nullable|string',
        ]);

        if ($user->isVerified()) {
            return response()->json(['success' => false, 'message' => 'المستخدم موثق بالفعل']);
        }

        $user->markAsVerified(Auth::id(), $request->admin_notes);

        return response()->json(['success' => true, 'message' => 'تم توثيق المستخدم بنجاح']);
    }

    /**
     * Unverify user (admin action).
     */
    public function unverifyUser(User $user)
    {
        if (!$user->isVerified()) {
            return response()->json(['success' => false, 'message' => 'المستخدم غير موثق بالفعل']);
        }

        $user->markAsUnverified();

        return response()->json(['success' => true, 'message' => 'تم إلغاء توثيق المستخدم بنجاح']);
    }
}
