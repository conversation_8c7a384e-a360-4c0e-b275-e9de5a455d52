<?php

namespace App\Services;

use App\Models\Category;
use App\Models\ContactMessage;
use App\Models\User;
use App\Models\UserVerification;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\TrafficLogs\Models\TrafficLogs;
use Modules\TrafficLogs\Models\TrafficLogsDetails;

class DashboardService
{
    public function getDashboardData()
    {
        return [
            'totalUsers' => $this->getTotalUsers(),
            'newUserPercentLastWeek' => $this->getNewUserPercentLastWeek(),
            'activeUsersToday' => $this->getActiveUsersToday(),
            'totalVisitors' => $this->getTotalVisitors(),
            'totalVisitorsToday' => $this->getTotalVisitorsToday(),
            'newVisitorPercentLastWeek' => $this->getNewVisitorPercentLastWeek(),
            'totalVisitorsThisWeek' => $this->getTotalVisitorsThisWeek(),
            'totalVisitorsThisMonth' => $this->getTotalVisitorsThisMonth(),
            'totalCategories' => $this->getTotalCategories(),
            'totalContactMessages' => $this->getTotalContactMessages(),
            'mostVisitedPagesThisMonth' => $this->getMostVisitedPagesThisMonth(),
            'weeklyVisits' => $this->getWeeklyVisits(),
            'weekLabels' => $this->getWeekLabels(),
            'statistics' => $this->getStatistics(),
            'dashboardCards' => $this->getDashboardCards(),
            'verificationStats' => $this->getVerificationStats(),
            'deviceStats' => $this->getDeviceStats(),
            'platformStats' => $this->getPlatformStats(),
            'countryStats' => $this->getCountryStats(),
        ];
    }

    private function getTotalUsers()
    {
        return User::count();
    }

    private function getActiveUsersToday()
    {
        return User::where('last_activity', '>=', now()->startOfDay())->count();
    }

    private function getNewUserPercentLastWeek()
    {
        $totalUsers = $this->getTotalUsers();
        $newUsersLastWeek = User::where('created_at', '>=', now()->subWeek())->count();
        return $newUsersLastWeek == 0 ? 0 : round(($newUsersLastWeek / $totalUsers) * 100, 2);
    }

    private function getTotalVisitors()
    {
        return TrafficLogs::count();
    }

    private function getTotalVisitorsToday()
    {
        return TrafficLogs::where('created_at', '>=', Carbon::today())
            ->groupBy('ip')
            ->count();
    }

    private function getNewVisitorPercentLastWeek()
    {
        $totalVisitors = $this->getTotalVisitors();
        $newVisitorsLastWeek = TrafficLogs::where('created_at', '>=', now()->subWeek())->count();
        return $newVisitorsLastWeek == 0 ? 0 : round(($newVisitorsLastWeek / $totalVisitors) * 100, 2);
    }

    private function getTotalVisitorsThisWeek()
    {
        return TrafficLogs::where('created_at', '>=', now()->startOfWeek())->count();
    }

    private function getTotalVisitorsThisMonth()
    {
        return TrafficLogs::where('created_at', '>=', now()->startOfMonth())->count();
    }

    private function getTotalCategories()
    {
        return Category::count();
    }

    private function getTotalContactMessages()
    {
        return ContactMessage::where('status', 'pending')->count();
    }

    private function getMostVisitedPagesThisMonth()
    {
        $totalCount = TrafficLogsDetails::where('created_at', '>=', now()->subMonth())->count();
        
        return TrafficLogsDetails::select('url', DB::raw('count(*) as visit_count'))
            ->where('created_at', '>=', now()->subMonth())
            ->groupBy('url')
            ->orderBy('visit_count', 'desc')
            ->limit(6)
            ->get()
            ->map(function ($page) use ($totalCount) {
                $urlParts = parse_url($page->url);
                $path = $urlParts['path'] ?? '/';
                $pageName = $path === '/' ? 'Homepage' : trim($path, '/');
                $visitCount = number_format($page->visit_count);
                $percentView = $totalCount == 0 ? 0 : round(($page->visit_count / $totalCount) * 100, 2);

                return [
                    'url' => $page->url,
                    'name' => $pageName,
                    'count' => $visitCount,
                    'percent_change' => abs($percentView),
                    'change_status' => 'success',
                ];
            });
    }

    private function getWeeklyVisits()
    {
        $visits = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $visits[] = TrafficLogs::whereDate('created_at', $date->toDateString())->count();
        }
        return $visits;
    }

    private function getWeekLabels()
    {
        $labels = [];
        for ($i = 6; $i >= 0; $i--) {
            $labels[] = now()->subDays($i)->format('D');
        }
        return $labels;
    }

    private function getDeviceStats()
    {
        return TrafficLogs::select('device', DB::raw('count(*) as count'))
            ->where('created_at', '>=', now()->subMonth())
            ->groupBy('device')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->device ?? 'Unknown',
                    'count' => $item->count,
                ];
            });
    }

    private function getPlatformStats()
    {
        $platformStats = TrafficLogs::select('operating_system', DB::raw('count(*) as count'))
            ->where('created_at', '>=', now()->subMonth())
            ->groupBy('operating_system')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->operating_system ?? 'Unknown',
                    'count' => $item->count,
                ];
            });

        // Separate top 8 platforms and group others into 'Other'
        $topPlatforms = $platformStats->take(8);

        $otherCount = $platformStats->slice(8)->sum('count');

        if ($otherCount > 0) {
            $topPlatforms->push([
                'name' => 'Other',
                'count' => $otherCount,
            ]);
        }

        return $topPlatforms;
    }

    private function getStatistics()
    {
        return [
           
            [
                'icon' => 'ti-category',
                'color' => 'danger',
                'value' => $this->getTotalCategories(),
                'label' => 'عدد التصنيفات',
            ],
            [
                'icon' => 'ti-messages',
                'color' => 'success',
                'value' => $this->getTotalContactMessages(),
                'label' => 'عدد رسائل التواصل',
            ],
            [
                'icon' => 'ti-shield-check',
                'color' => 'info',
                'value' => $this->getTotalVerifiedUsers(),
                'label' => 'المستخدمين الموثقين',
            ],
            [
                'icon' => 'ti-clock',
                'color' => 'warning',
                'value' => $this->getPendingVerifications(),
                'label' => 'طلبات التوثيق المعلقة',
            ],
        ];
    }

    private function getDashboardCards()
    {
        return [
            [
                'title' => 'عدد المستخدمين',
                'icon' => 'ti-user',
                'color' => 'primary',
                'value' => $this->getTotalUsers(),
                'highlight' => $this->getActiveUsersToday() . ' مستخدم نشط اليوم',
                'percentage' => '+' . $this->getNewUserPercentLastWeek(),
                'description' => 'من الأسبوع الماضي',
            ],
            [
                'title' => 'عدد الزوار',
                'icon' => 'ti-users-group',
                'color' => 'warning',
                'value' => $this->getTotalVisitors(),
                'highlight' => $this->getTotalVisitorsToday() . ' زائر اليوم',
                'percentage' => '+' . $this->getNewVisitorPercentLastWeek(),
                'description' => 'من الأسبوع الماضي (' . $this->getTotalVisitorsThisWeek() . ' زائر)',
            ],
        
        ];
    }

    private function getCountryStats()
    {
        $countryStats = TrafficLogs::select('country', DB::raw('count(*) as count'))
            ->where('created_at', '>=', now()->subMonth())
            ->groupBy('country')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->country ?? 'Unknown',
                    'count' => $item->count,
                ];
            });

        // Separate top 5 known countries and group others into 'Other'
        $topCountries = $countryStats->filter(function ($item) {
            return $item['name'] !== 'Unknown' || $item['name'] !== 'غير محدد';
        })->take(5);

        $otherCount = $countryStats->filter(function ($item) use ($topCountries) {
            return $item['name'] === 'Unknown' || !$topCountries->contains('name', $item['name']);
        })->sum('count');

        if ($otherCount > 0) {
            $topCountries->push([
                'name' => 'Other',
                'count' => $otherCount,
            ]);
        }

        return $topCountries;
    }

    private function getTotalVerifiedUsers()
    {
        return User::where('is_verified', true)->count();
    }

    private function getPendingVerifications()
    {
        return UserVerification::where('status', 'pending')->count();
    }

    private function getVerificationStats()
    {
        return [
            'totalVerified' => $this->getTotalVerifiedUsers(),
            'pendingVerifications' => $this->getPendingVerifications(),
            'rejectedVerifications' => UserVerification::where('status', 'rejected')->count(),
            'approvedVerifications' => UserVerification::where('status', 'approved')->count(),
            'verificationRate' => $this->getTotalUsers() > 0 ? round(($this->getTotalVerifiedUsers() / $this->getTotalUsers()) * 100, 2) : 0,
        ];
    }
}