<?php

namespace App\Models;

use App\RolesEnum;
use App\Traits\WithHashId;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use App\Models\Achievement;
use App\Traits\Followable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Notifications\CustomVerifyEmail;
use App\Notifications\CustomResetPassword;
use App\Notifications\EmailOtpVerification;
use App\Models\UserSession;
use App\Models\UserRate;
use App\Models\Otp;

class User extends Authenticatable implements MustVerifyEmail
{
    use WithHashId, HasFactory, Notifiable, HasApiTokens, HasRoles, SoftDeletes, Followable;

    /**
     * The attributes that are not mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = ['password', 'remember_token'];

    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new CustomVerifyEmail());
    }

    /**
     * Send the email OTP verification notification.
     *
     * @return void
     */
    public function sendEmailOtpVerificationNotification()
    {
        // Generate a new OTP
        $otp = Otp::generate($this, 'email');

        // Send the OTP notification
        $this->notify(new EmailOtpVerification($otp->code));

        return $otp;
    }

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new CustomResetPassword($token));
    }

    public function isSuperAdmin()
    {
        return $this->hasRole(RolesEnum::SUPER_ADMIN);
    }
    public function isAdmin()
    {
        return $this->hasRole(RolesEnum::ADMIN);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'device_ids' => 'array',
            'has_multiple_devices' => 'boolean',
        ];
    }

    //* override insert function to hash password
    public function insert(array $attributes = [])
    {
        parent::insert($attributes);
    }

    //* On create save hashID
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            // Generate a unique username if not provided
            if (empty($model->username)) {
                $base = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $model->name));
                $username = $base;
                while (self::where('username', $username)->exists() || empty($username)) {
                    $username = $base . rand(1000, 9999);
                }
                $model->username = $username;
            }
        });
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });
    }

    /*
    |--------------------------------------------------------------------------
    | Scopes
    |--------------------------------------------------------------------------
    */

    /**
     * Scope a query to search users by name or email.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('name', 'like', '%' . $search . '%')
                     ->orWhere('email', 'like', '%' . $search . '%');
    }

    /**
     * Scope a query to only include verified users.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVerified($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    /**
     * Scope a query to only include users with a specific role.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $role
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithRole($query, $role)
    {
        return $query->whereHas('roles', function($q) use ($role) {
            $q->where('name', $role);
        });
    }

    /*
    |--------------------------------------------------------------------------
    | Relationships
    |--------------------------------------------------------------------------
    */

    /**
     * Get the notifications for the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function userNotifications()
    {
        return $this->belongsToMany(AppNotification::class, 'app_user_notifications');
    }

    /**
     * The achievements that belong to the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function achievements(): BelongsToMany
    {
        return $this->belongsToMany(Achievement::class, 'user_achievements')
                    ->withTimestamps()
                    ->withPivot('achieved_at');
    }

    /**
     * Get the favorite products for the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function favorites(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'user_favorites');
    }

    /**
     * Get the products that belong to the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the product comments that belong to the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function productComments()
    {
        return $this->hasMany(ProductComment::class);
    }

    /**
     * Get the users that this user follows.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function follows()
    {
        return $this->hasMany(Follow::class, 'user_id');
    }

    /**
     * Get all sessions for the user.
     */
    public function sessions(): HasMany
    {
        return $this->hasMany(UserSession::class);
    }

    /**
     * Get active sessions for the user.
     */
    public function activeSessions(): HasMany
    {
        return $this->hasMany(UserSession::class)->where('is_active', true);
    }

    /**
     * Check if user has multiple devices or shares device with other users.
     */
    public function checkMultipleDevices()
    {
        // Get all device IDs for this user
        $userDeviceIds = $this->sessions()
            ->whereNotNull('device_id')
            ->distinct()
            ->pluck('device_id')
            ->toArray();

        // Update device_ids field
        $this->device_ids = $userDeviceIds;

        // Check if any of the user's device IDs are used by other users
        $sharedDevices = UserSession::whereIn('device_id', $userDeviceIds)
            ->where('user_id', '!=', $this->id)
            ->exists();

        // Update has_multiple_devices flag
        $this->has_multiple_devices = $sharedDevices || count($userDeviceIds) > 1;
        $this->save();

        return $this->has_multiple_devices;
    }

    /**
     * Get ratings given by this user.
     */
    public function givenRates(): HasMany
    {
        return $this->hasMany(UserRate::class, 'rater_id');
    }

    /**
     * Get ratings received by this user.
     */
    public function receivedRates(): HasMany
    {
        return $this->hasMany(UserRate::class, 'rated_user_id');
    }

    /**
     * Get approved ratings received by this user.
     */
    public function approvedRates(): HasMany
    {
        return $this->hasMany(UserRate::class, 'rated_user_id')->where('status', 'approved');
    }

    /**
     * Get average rating for this user.
     */
    public function getAverageRatingAttribute()
    {
        return $this->approvedRates()->avg('rate') ?? 0;
    }

    /**
     * Get total number of ratings for this user.
     */
    public function getRatingsCountAttribute()
    {
        return $this->approvedRates()->count();
    }


    /**
     * Get the user's avatar URL attribute.
     *
     * @return string
     */
    public function getAvatarUrlAttribute()
    {
        if($this->photo == null){
            return asset('assets/img/avatar.png');
        }
        return asset('storage/avatars/' . $this->photo);
    }

    /**
     * Get user's initials (first two letters of name)
     * If name has multiple words, takes first letter of first two words
     * If name has only one word, takes first two letters of that word
     *
     * @return string
     */
    public function getInitialsAttribute()
    {
        $name = trim($this->name);
        $nameParts = explode(' ', $name);

        if (count($nameParts) >= 2) {
            // If name has multiple words, take first letter of first two words
            return strtoupper(mb_substr($nameParts[0], 0, 1) . mb_substr($nameParts[1], 0, 1));
        } else {
            // If name has only one word, take first two letters
            return strtoupper(mb_substr($name, 0, 2));
        }
    }

    /**
     * Get the user's avatar - either the image URL or initials
     *
     * @param bool $returnInitials Whether to return initials when no photo exists
     * @return string|array
     */
    public function avatar($returnInitials = false)
    {
        if($this->photo == null){
            if ($returnInitials) {
                return [
                    'type' => 'initials',
                    'initials' => $this->initials,
                    'color' => $this->getAvatarColorAttribute()
                ];
            }
            return asset('assets/img/avatar.png');
        }
        return asset('storage/avatars/' . $this->photo);
    }

    /**
     * Get a consistent color based on user ID for avatar background
     *
     * @return string
     */
    public function getAvatarColorAttribute()
    {
        // Array of tailwind background colors
        $colors = [
            'bg-blue-300',
            'bg-red-300',
            'bg-green-300',
            'bg-yellow-300',
            'bg-purple-300',
            'bg-pink-300',
            'bg-indigo-300',
            'bg-teal-300',
            'bg-orange-300',
            'bg-cyan-300',
        ];

        // Use user ID to consistently pick a color
        $colorIndex = $this->id % count($colors);
        return $colors[$colorIndex];
    }

}
