<?php

namespace App\Models;

use App\Enums\ConditionEnum;
use App\Enums\StatusEnum;
use App\Traits\Followable;
use App\Traits\WithHashId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class Product extends Model
{
    use HasFactory, WithHashId, HasSlug, SoftDeletes, Followable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'category_id',
        'title',
        'desc',
        'price',
        'location_id',
        'status',
        'condition',
        'slug',
        'views_count',
        'allow_phone_call',
        'allow_chat',
        'alternative_phone',
        'created_by',
        'updated_by',
        'deleted_by',
        'brand_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = ['created_by', 'updated_by', 'deleted_by'];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'views_count' => 'integer',
        'status' => StatusEnum::class,
        'condition' => ConditionEnum::class,
        'allow_phone_call' => 'boolean',
        'allow_chat' => 'boolean',
    ];


    /**
     * Get the options for generating the slug.
     *
     * @return \Spatie\Sluggable\SlugOptions
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    /**
     * Bootstrap the model and its traits.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {});
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });
    }

    /*
    |--------------------------------------------------------------------------
    | Relationships
    |--------------------------------------------------------------------------
    */

    /**
     * Get the user that owns the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category that the product belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the location that the product belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    /**
     * Get the tags associated with the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'product_tags');
    }

    /**
     * Get all media for the product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function media()
    {
        return $this->hasMany(ProductMedia::class);
    }

    /**
     * Get the primary media for the product.
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function primaryMedia()
    {
        return $this->hasMany(ProductMedia::class)->where('is_primary', true)->first();
    }

    /**
     * Get the primary media URL for the product.
     *
     * @return string|null
     */
    public function primaryMediaUrl()
    {
        $primaryMedia = $this->media()->where('is_primary', true)->first();
        $path = $primaryMedia ? $primaryMedia->thumbnail_path : null;
        $path ??= $primaryMedia ? $primaryMedia->small_path : null;
        $path ??= $primaryMedia ? $primaryMedia->path : null;

        if (!$path) {
            return null;
        }

        return asset('storage/products/' . $path);
    }

    /**
     * Get media of a specific type for the product.
     *
     * @param string $type Type of media (thumbnail, image, video, etc.)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getMedia($type = 'thumbnail')
    {
        $media = $this->media()->where('type', $type)->get();
        return $media;
    }

    /**
     * Get the user who created this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who deleted this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function deletedBy()
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Get the brand that the product belongs to.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /*
    |--------------------------------------------------------------------------
    | Scopes
    |--------------------------------------------------------------------------
    */

    /**
     * Scope a query to search products by title or description.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('title', 'like', '%' . $search . '%')
                     ->orWhere('desc', 'like', '%' . $search . '%');
    }

    /**
     * Scope a query to filter products by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \App\Enums\StatusEnum  $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter products by condition.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  \App\Enums\ConditionEnum  $condition
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCondition($query, $condition)
    {
        return $query->where('condition', $condition);
    }

    /**
     * Scope a query to filter products by category.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $categoryId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope a query to filter products by location.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $locationId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByLocation($query, $locationId)
    {
        return $query->where('location_id', $locationId);
    }

    /**
     * Scope a query to filter products by user.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to filter products by price range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  float  $min
     * @param  float  $max
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByPriceRange($query, $min, $max)
    {
        return $query->whereBetween('price', [$min, $max]);
    }

    /**
     * Scope a query to get active products.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        // Using string value instead of enum constant to avoid undefined constant error
        return $query->where('status', 'published');
    }

    /*
    |--------------------------------------------------------------------------
    | Methods
    |--------------------------------------------------------------------------
    */

    /**
     * Increment the view count for this product.
     *
     * @return void
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * Get the comments for this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function comments()
    {
        return $this->hasMany(ProductComment::class);
    }

    /**
     * Get the analytics for this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function analytics()
    {
        return $this->hasMany(ProductAnalytics::class);
    }

    /**
     * Register analytics for this product.
     *
     * @return void
     */
    public function registerAnalytics()
    {
        $userId = null;
        if (auth()->user()) {
            $userId = auth()->user()->getKey();
        }

        $this->analytics()->create([
            'user_id' => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'device' => $this->getDeviceInfo(request()->userAgent()),
            'viewed_at' => now(),
        ]);
    }

    /**
     * Get the favorites for this product.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function favorites()
    {
        return $this->hasMany(UserFavorite::class);
    }

    /**
     * Get device information from user agent string.
     *
     * @param string $userAgent
     * @return string
     */
    private function getDeviceInfo($userAgent)
    {
        $device = 'Unknown';

        if (preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $userAgent)) {
            $device = 'Mobile';
        } elseif (preg_match('/android|ipad|playbook|silk/i', $userAgent)) {
            $device = 'Tablet';
        } elseif (preg_match('/macintosh|mac os x/i', $userAgent)) {
            $device = 'Mac';
        } elseif (preg_match('/windows|win32/i', $userAgent)) {
            $device = 'Windows';
        } elseif (preg_match('/linux/i', $userAgent)) {
            $device = 'Linux';
        }

        return $device;
    }
}