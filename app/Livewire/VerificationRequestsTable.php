<?php

namespace App\Livewire;

use App\Livewire\DataTable;
use App\Models\UserVerification;
use Carbon\Carbon;

class VerificationRequestsTable extends DataTable
{
    public $statusFilter = 'all';

    protected $listeners = ['verificationUpdated' => '$refresh'];

    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن طلب التوثيق بالاسم أو البريد الالكتروني';
        
        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'المستخدم', 'type' => 'string', 'field' => 'user_name', 'renderType' => 'user_name'],
            ['title' => 'نوع المستخدم', 'type' => 'string', 'field' => 'user_type'],
            ['title' => 'الحالة', 'type' => 'status', 'field' => 'status', 'renderType' => 'status'],
            ['title' => 'تاريخ الطلب', 'type' => 'date', 'field' => 'created_at', 'renderType' => 'created_at'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
        ];
        
        $this->actions = ['approve', 'reject', 'view_details'];
        if($this->visibleColumns == null) $this->visibleColumns = array_column($this->columns, 'field');
    }

    public function render()
    {
        $query = UserVerification::with(['user'])
            ->when($this->statusFilter !== 'all', function ($query) {
                return $query->where('status', $this->statusFilter);
            })
            ->when($this->search, function ($query) {
                return $query->whereHas('user', function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            })
            ->orderBy($this->sortField, $this->sortDirection);
            
        $verifications = $query->paginate(12);

        $this->resetPage();

        return view('components.table.common-table', [
            'data' => $verifications,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
        ]);
    }

    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }

        if ($column['renderType'] == 'user_name') {
            return $item->user->name . '<br><small class="text-muted">' . $item->user->email . '</small>';
        }

        if ($column['renderType'] == 'status') {
            $statusClasses = [
                'pending' => 'bg-warning',
                'approved' => 'bg-success',
                'rejected' => 'bg-danger',
            ];
            $statusLabels = [
                'pending' => 'قيد المراجعة',
                'approved' => 'مقبول',
                'rejected' => 'مرفوض',
            ];
            $class = $statusClasses[$item->status] ?? 'bg-secondary';
            $label = $statusLabels[$item->status] ?? $item->status;
            return '<span class="badge ' . $class . '">' . $label . '</span>';
        }

        if ($column['renderType'] == 'created_at') {
            return Carbon::parse($item->created_at)->diffForHumans();
        }

        return $item->{$column['field']};
    }

    public function renderAction($actionType, $item)
    {
        $actions = [
            'approve' => [
                'icon' => 'ti-check',
                'title' => 'Approve',
                'color' => 'text-success',
                'onclick' => "approveVerification({$item->id})",
                'url' => 'javascript:void(0)',
            ],
            'reject' => [
                'icon' => 'ti-x',
                'title' => 'Reject',
                'color' => 'text-danger',
                'onclick' => "rejectVerification({$item->id})",
                'url' => 'javascript:void(0)',
            ],
            'view_details' => [
                'icon' => 'ti-eye',
                'title' => 'View Details',
                'color' => 'text-info',
                'onclick' => "Livewire.dispatch('openVerificationModal', { verificationId: {$item->id} })",
                'url' => 'javascript:void(0)',
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            
            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
}
