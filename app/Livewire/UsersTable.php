<?php

namespace App\Livewire;

use App\Livewire\DataTable;
use App\Models\User;
use App\Models\UserVerification;
use Carbon\Carbon;

class UsersTable extends DataTable
{
    public $verificationFilter = 'all';


    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن المستخدم بالاسم أو البريد الالكتروني';
        
        $this->columns = [
                ['title' => '#', 'type' => 'number', 'field' => 'id'],
                ['title' => 'الاسم', 'type' => 'string', 'field' => 'name'],
                ['title' => 'البريد الالكتروني', 'type' => 'string', 'field' => 'email'],
                ['title' => 'حالة التوثيق', 'type' => 'verification', 'field' => 'is_verified', 'renderType' => 'verification'],
                ['title' => 'اخر نشاط', 'type' => 'date', 'field' => 'last_activity','renderType' => 'last_activity'],
                ['title' => 'FCM', 'type' => 'fcm', 'field' => 'fcm_token','renderType' => 'fcm'],
                ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions' ,'sortable' => false],
            ];
        
        $this->actions = ['delete', 'view', 'edit', 'verify', 'unverify', 'view_verification'];
        if($this->visibleColumns == null) $this->visibleColumns = array_column($this->columns, 'field');
    }
    

    public function render()
    {
        $users = User::search($this->search)
            ->when($this->verificationFilter !== 'all', function ($query) {
                if ($this->verificationFilter === 'verified') {
                    return $query->where('is_verified', true);
                } elseif ($this->verificationFilter === 'unverified') {
                    return $query->where('is_verified', false);
                } elseif ($this->verificationFilter === 'pending') {
                    return $query->whereHas('pendingVerification');
                }
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(12);

            $this->resetPage();

        return view('components.table.common-table', [
            'data' => $users,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
        ]);
    }


    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }

        if ($column['renderType'] == 'last_activity') {
            return $item->last_activity==null ? 'N/A' :  Carbon::parse($item->last_activity)->diffForHumans() ;
        }

        if ($column['renderType'] == 'verification') {
            if ($item->is_verified) {
                return '<span class="badge bg-success"><i class="ti ti-shield-check me-1"></i>موثق</span>';
            } elseif ($item->hasPendingVerification()) {
                return '<span class="badge bg-warning"><i class="ti ti-clock me-1"></i>قيد المراجعة</span>';
            } else {
                return '<span class="badge bg-secondary"><i class="ti ti-shield-x me-1"></i>غير موثق</span>';
            }
        }

    //     <td>
    //     @if ($user->fcm_token != null && $user->fcm_token != '')
    //         <a href="javascript:void(0)" onclick="openNotificationModal({{ $user }})"
    //             class="btn btn-icon btn-text-secondary">
    //             <i class="ti ti-bell ti-md"></i>
    //         </a>
    //     @else
    //         N/A
    //     @endif
    // </td>
        if ($column['renderType'] == 'fcm') {
            return $item->fcm_token != null && $item->fcm_token != '' ? "<a href='javascript:void(0)' onclick='openNotificationModal(" . json_encode($item) . ")' class='btn btn-icon btn-text-secondary'><i class='ti ti-bell ti-md'></i></a>" : 'N/A';
        }


    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $model = 'user';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف المستخدم '. $item->name . ' ؟';
        $item['delete'] = route('users.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
            'view' => [
                'icon' => 'ti-eye',
                'title' => 'View',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/{$model}s/{$item->id}",
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/{$model}s/{$item->id}/edit",
            ],
            'verify' => [
                'icon' => 'ti-shield-check',
                'title' => 'Verify User',
                'color' => 'text-success',
                'onclick' => "verifyUser({$item->id})",
                'url' => 'javascript:void(0)',
            ],
            'unverify' => [
                'icon' => 'ti-shield-x',
                'title' => 'Unverify User',
                'color' => 'text-warning',
                'onclick' => "unverifyUser({$item->id})",
                'url' => 'javascript:void(0)',
            ],
            'view_verification' => [
                'icon' => 'ti-file-text',
                'title' => 'View Verification',
                'color' => 'text-info',
                'onclick' => "viewVerification({$item->id})",
                'url' => 'javascript:void(0)',
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            if($actionType == 'delete') {
                return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
            }
            // Undefined array key "url"
            if(!isset($action['url']) || $action['url'] == null) $action['url'] = 'javascript:void(0)';
             
            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
}
